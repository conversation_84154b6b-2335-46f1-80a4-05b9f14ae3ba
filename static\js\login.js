/**
 * 登录页面JavaScript
 * 处理用户登录逻辑和前端加密
 */

class LoginManager {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.loginBtn = document.getElementById('loginBtn');
        this.debugPanel = document.getElementById('debugPanel');
        
        this.init();
    }
    
    /**
     * 初始化登录管理器
     */
    init() {
        if (!this.form) return;
        
        // 绑定表单提交事件
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
        
        // 绑定输入框事件
        this.usernameInput?.addEventListener('input', this.clearErrors.bind(this));
        this.passwordInput?.addEventListener('input', this.clearErrors.bind(this));
        
        // 绑定回车键快捷登录
        this.passwordInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSubmit(e);
            }
        });
        
        console.log('🔐 登录管理器已初始化');
        console.log('💡 提示：打开浏览器开发者工具查看加密过程');
    }
    
    /**
     * 处理表单提交
     */
    async handleSubmit(event) {
        event.preventDefault();
        
        // 获取表单数据
        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value;
        
        // 基础验证
        if (!this.validateInput(username, password)) {
            return;
        }
        
        // 设置按钮加载状态
        CommonUtils.setButtonLoading(this.loginBtn, true);
        
        try {
            // 加密登录数据
            const encryptedData = LoginEncryptor.encryptLoginData(username, password);
            
            if (!encryptedData) {
                throw new Error('数据加密失败');
            }
            
            // 更新调试面板
            this.updateDebugPanel(username, password, encryptedData);
            
            // 发送登录请求
            const response = await CommonUtils.request('/api/login/', {
                method: 'POST',
                data: encryptedData
            });
            
            if (response.success) {
                CommonUtils.showSuccess('登录成功！正在跳转...');
                
                // 延迟跳转，让用户看到成功消息
                setTimeout(() => {
                    window.location.href = '/dashboard/';
                }, 1000);
            } else {
                throw new Error(response.message || '登录失败');
            }
            
        } catch (error) {
            console.error('登录错误:', error);
            CommonUtils.showError(error.message || '登录失败，请重试');
        } finally {
            // 恢复按钮状态
            CommonUtils.setButtonLoading(this.loginBtn, false);
        }
    }
    
    /**
     * 验证输入数据
     */
    validateInput(username, password) {
        if (!username) {
            CommonUtils.showError('请输入用户名');
            this.usernameInput.focus();
            return false;
        }
        
        if (username.length < 3) {
            CommonUtils.showError('用户名长度至少3个字符');
            this.usernameInput.focus();
            return false;
        }
        
        if (!password) {
            CommonUtils.showError('请输入密码');
            this.passwordInput.focus();
            return false;
        }
        
        if (password.length < 6) {
            CommonUtils.showError('密码长度至少6个字符');
            this.passwordInput.focus();
            return false;
        }
        
        return true;
    }
    
    /**
     * 清除错误状态
     */
    clearErrors() {
        // 移除输入框的错误样式
        this.usernameInput?.classList.remove('error');
        this.passwordInput?.classList.remove('error');
    }
    
    /**
     * 更新调试面板信息
     */
    updateDebugPanel(username, password, encryptedData) {
        if (!this.debugPanel) return;
        
        // 更新调试信息
        const debugElements = {
            debugUsername: username,
            debugPassword: password,
            debugEncryptedUsername: encryptedData.encrypted_username,
            debugEncryptedPassword: encryptedData.encrypted_password,
            debugTimestamp: encryptedData.timestamp,
            debugKey: ENCRYPTION_CONFIG.SECRET_KEY
        };
        
        Object.entries(debugElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
        
        // 在控制台输出详细信息
        console.group('🔐 登录数据加密详情');
        console.log('原始用户名:', username);
        console.log('原始密码:', password);
        console.log('加密用户名:', encryptedData.encrypted_username);
        console.log('加密密码:', encryptedData.encrypted_password);
        console.log('时间戳:', encryptedData.timestamp);
        console.log('数据签名:', encryptedData.signature);
        console.log('加密配置:', ENCRYPTION_CONFIG);
        console.groupEnd();
        
        // 显示加密算法信息
        console.group('🔧 加密算法信息');
        console.log('加密算法: AES-256-CBC');
        console.log('密钥派生: PBKDF2-SHA256');
        console.log('迭代次数:', ENCRYPTION_CONFIG.ITERATIONS);
        console.log('密钥长度:', ENCRYPTION_CONFIG.KEY_LENGTH, 'bits');
        console.log('IV长度:', ENCRYPTION_CONFIG.IV_LENGTH, 'bits');
        console.groupEnd();
    }
    
    /**
     * 演示加密过程（用于逆向分析学习）
     */
    demonstrateEncryption() {
        console.group('🎓 加密过程演示');
        
        const testData = 'demo_user';
        console.log('1. 原始数据:', testData);
        
        const key = CryptoUtils.generateKey();
        console.log('2. 生成的密钥:', key.toString());
        
        const iv = CryptoUtils.generateIV();
        console.log('3. 生成的IV:', iv.toString());
        
        const encrypted = CryptoUtils.encryptData(testData);
        console.log('4. 加密结果:', encrypted);
        
        const decrypted = CryptoUtils.decryptData(encrypted);
        console.log('5. 解密结果:', decrypted);
        
        console.log('6. 验证结果:', testData === decrypted ? '✅ 成功' : '❌ 失败');
        
        console.groupEnd();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化登录管理器
    const loginManager = new LoginManager();
    
    // 将登录管理器暴露到全局作用域，便于调试
    window.loginManager = loginManager;
    
    // 添加一些调试命令
    window.demoEncryption = () => loginManager.demonstrateEncryption();
    window.testCrypto = () => DebugUtils.testEncryption();
    window.showConfig = () => DebugUtils.showConfig();
    
    // 在控制台显示可用的调试命令
    console.group('🛠️ 可用的调试命令');
    console.log('demoEncryption() - 演示加密过程');
    console.log('testCrypto() - 测试加密解密功能');
    console.log('showConfig() - 显示加密配置');
    console.log('loginManager - 访问登录管理器实例');
    console.groupEnd();
    
    // 检查是否有CryptoJS库
    if (typeof CryptoJS === 'undefined') {
        console.error('❌ CryptoJS库未加载，请检查库文件');
        CommonUtils.showError('加密库加载失败，请刷新页面重试');
    } else {
        console.log('✅ CryptoJS库已加载');
    }
});

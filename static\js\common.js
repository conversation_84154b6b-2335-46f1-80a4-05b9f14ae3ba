/**
 * 通用JavaScript工具函数
 * 用于整个应用的公共功能
 */

// 通用工具类
class CommonUtils {
    
    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 ('success' | 'error' | 'info')
     * @param {number} duration - 显示时长（毫秒）
     */
    static showMessage(message, type = 'info', duration = 3000) {
        const messageBox = document.getElementById('messageBox');
        const messageText = document.getElementById('messageText');
        
        if (!messageBox || !messageText) return;
        
        // 设置消息内容和样式
        messageText.textContent = message;
        messageBox.className = `message-box ${type}`;
        messageBox.style.display = 'flex';
        
        // 自动隐藏
        setTimeout(() => {
            messageBox.style.display = 'none';
        }, duration);
    }
    
    /**
     * 显示成功消息
     */
    static showSuccess(message, duration = 3000) {
        this.showMessage(message, 'success', duration);
    }
    
    /**
     * 显示错误消息
     */
    static showError(message, duration = 5000) {
        this.showMessage(message, 'error', duration);
    }
    
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * 验证密码强度
     * @param {string} password - 密码
     * @returns {Object} 验证结果
     */
    static validatePassword(password) {
        const result = {
            isValid: false,
            errors: []
        };
        
        if (password.length < 6) {
            result.errors.push('密码长度至少6个字符');
        }
        
        if (!/[a-zA-Z]/.test(password)) {
            result.errors.push('密码必须包含字母');
        }
        
        if (!/[0-9]/.test(password)) {
            result.errors.push('密码建议包含数字');
        }
        
        result.isValid = result.errors.length === 0;
        return result;
    }
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }
    
    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 节流后的函数
     */
    static throttle(func, delay) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }
    
    /**
     * 获取CSRF Token
     * @returns {string} CSRF Token
     */
    static getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    /**
     * 发送AJAX请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求Promise
     */
    static async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            credentials: 'same-origin'
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        // 如果是POST请求且有数据，转换为JSON
        if (finalOptions.method === 'POST' && finalOptions.data) {
            finalOptions.body = JSON.stringify(finalOptions.data);
            delete finalOptions.data;
        }
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }
            
            return data;
        } catch (error) {
            console.error('请求错误:', error);
            throw error;
        }
    }
    
    /**
     * 设置按钮加载状态
     * @param {HTMLElement} button - 按钮元素
     * @param {boolean} loading - 是否加载中
     */
    static setButtonLoading(button, loading) {
        const btnText = button.querySelector('.btn-text');
        const btnLoading = button.querySelector('.btn-loading');
        
        if (loading) {
            button.disabled = true;
            if (btnText) btnText.style.display = 'none';
            if (btnLoading) btnLoading.style.display = 'inline';
        } else {
            button.disabled = false;
            if (btnText) btnText.style.display = 'inline';
            if (btnLoading) btnLoading.style.display = 'none';
        }
    }
    
    /**
     * 格式化时间
     * @param {Date|string} date - 日期
     * @returns {string} 格式化后的时间字符串
     */
    static formatDateTime(date) {
        const d = new Date(date);
        return d.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    
    /**
     * 复制文本到剪贴板
     * @param {string} text - 要复制的文本
     * @returns {Promise<boolean>} 是否成功
     */
    static async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            const success = document.execCommand('copy');
            document.body.removeChild(textArea);
            return success;
        }
    }
}

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    
    // 初始化消息框关闭按钮
    const closeMessageBtn = document.getElementById('closeMessage');
    if (closeMessageBtn) {
        closeMessageBtn.addEventListener('click', function() {
            const messageBox = document.getElementById('messageBox');
            if (messageBox) {
                messageBox.style.display = 'none';
            }
        });
    }
    
    // 初始化调试面板切换
    const toggleDebugBtn = document.getElementById('toggleDebug');
    const debugPanel = document.getElementById('debugPanel');
    
    if (toggleDebugBtn && debugPanel) {
        toggleDebugBtn.addEventListener('click', function() {
            const isVisible = debugPanel.style.display !== 'none';
            debugPanel.style.display = isVisible ? 'none' : 'block';
            toggleDebugBtn.textContent = isVisible ? '显示调试信息' : '隐藏调试信息';
        });
    }
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
        CommonUtils.showError('发生了一个错误，请刷新页面重试');
    });
    
    // 全局未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
        CommonUtils.showError('网络请求失败，请检查网络连接');
    });
});

// 将工具类暴露到全局作用域
window.CommonUtils = CommonUtils;

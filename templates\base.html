<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}JS逆向登录系统{% endblock %}</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <!-- 引入CryptoJS库用于前端加密 -->
    <script src="/static/lib/crypto-js.min.js"></script>
    <script src="/static/js/encryption.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>JS逆向登录测试系统</h1>
            <p class="subtitle">用于逆向工程分析的加密登录演示</p>
        </header>
        
        <main class="main-content">
            {% block content %}
            {% endblock %}
        </main>
        
        <footer class="footer">
            <p>&copy; 2024 JS逆向登录系统 - 仅用于教育和测试目的</p>
        </footer>
    </div>
    
    <!-- 通用JavaScript -->
    <script src="/static/js/common.js"></script>
    {% block scripts %}
    {% endblock %}
</body>
</html>

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, LoginLog


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """
    自定义用户管理界面
    """
    list_display = ['username', 'email', 'phone', 'login_count', 'last_login_ip', 'created_at', 'is_active']
    list_filter = ['is_active', 'is_staff', 'created_at']
    search_fields = ['username', 'email', 'phone']
    ordering = ['-created_at']
    
    fieldsets = UserAdmin.fieldsets + (
        ('扩展信息', {
            'fields': ('phone', 'login_count', 'last_login_ip')
        }),
    )


@admin.register(LoginLog)
class LoginLogAdmin(admin.ModelAdmin):
    """
    登录日志管理界面
    """
    list_display = ['user', 'login_time', 'ip_address', 'login_success']
    list_filter = ['login_success', 'login_time']
    search_fields = ['user__username', 'ip_address']
    ordering = ['-login_time']
    readonly_fields = ['user', 'login_time', 'ip_address', 'user_agent', 'encrypted_data', 'login_success']
    
    def has_add_permission(self, request):
        return False  # 禁止手动添加日志

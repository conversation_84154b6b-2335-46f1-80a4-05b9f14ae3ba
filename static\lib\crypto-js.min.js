// 简化的CryptoJS实现 - 仅用于演示
// 在实际项目中应使用完整的CryptoJS库

(function() {
    'use strict';

    // 检查浏览器支持
    if (typeof crypto === 'undefined' || typeof crypto.getRandomValues !== 'function') {
        console.error('浏览器不支持Web Crypto API');
    }

    // 简单的Base64编码/解码
    const Base64 = {
        encode: function(str) {
            try {
                return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
                    return String.fromCharCode('0x' + p1);
                }));
            } catch (e) {
                console.error('Base64编码失败:', e);
                return null;
            }
        },
        decode: function(str) {
            try {
                return decodeURIComponent(atob(str).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
            } catch (e) {
                console.error('Base64解码失败:', e);
                return null;
            }
        }
    };

    // 生成随机字符串
    function generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 简单的AES加密实现（仅用于演示）
    const AES = {
        encrypt: function(message, key, options) {
            try {
                // 生成随机IV
                const iv = options && options.iv ? options.iv : this.generateIV();

                // 简单的加密实现（XOR + Base64）
                const keyStr = key.toString();
                const ivStr = iv.toString();
                const encrypted = this.simpleEncrypt(message, keyStr, ivStr);

                // 将IV和加密数据组合
                const combined = ivStr + '|' + encrypted;

                return {
                    ciphertext: {
                        toString: function() {
                            return Base64.encode(combined);
                        }
                    }
                };
            } catch (e) {
                console.error('AES加密失败:', e);
                return null;
            }
        },

        decrypt: function(cipherParams, key, options) {
            try {
                const combined = Base64.decode(cipherParams.ciphertext.toString());
                if (!combined) return null;

                const parts = combined.split('|');
                if (parts.length !== 2) return null;

                const ivStr = parts[0];
                const encrypted = parts[1];
                const keyStr = key.toString();

                const decrypted = this.simpleDecrypt(encrypted, keyStr, ivStr);

                return {
                    toString: function() {
                        return decrypted;
                    }
                };
            } catch (e) {
                console.error('AES解密失败:', e);
                return null;
            }
        },

        simpleEncrypt: function(text, key, iv) {
            try {
                let result = '';
                const keyStr = key + iv;
                for (let i = 0; i < text.length; i++) {
                    const keyChar = keyStr.charCodeAt(i % keyStr.length);
                    const textChar = text.charCodeAt(i);
                    result += String.fromCharCode(textChar ^ keyChar);
                }
                return Base64.encode(result);
            } catch (e) {
                console.error('简单加密失败:', e);
                return null;
            }
        },

        simpleDecrypt: function(encrypted, key, iv) {
            try {
                const decoded = Base64.decode(encrypted);
                if (!decoded) return null;

                let result = '';
                const keyStr = key + iv;
                for (let i = 0; i < decoded.length; i++) {
                    const keyChar = keyStr.charCodeAt(i % keyStr.length);
                    const textChar = decoded.charCodeAt(i);
                    result += String.fromCharCode(textChar ^ keyChar);
                }
                return result;
            } catch (e) {
                console.error('简单解密失败:', e);
                return null;
            }
        },

        generateIV: function() {
            try {
                return {
                    toString: function() {
                        return generateRandomString(32);
                    }
                };
            } catch (e) {
                console.error('生成IV失败:', e);
                return {
                    toString: function() {
                        return 'default_iv_' + Date.now();
                    }
                };
            }
        }
    };

    // 哈希函数
    const Hash = {
        MD5: function(message) {
            try {
                // 简化的MD5实现（仅用于演示）
                const hash = btoa(message + 'md5_salt').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
                return {
                    toString: function() {
                        return (hash + '00000000000000000000000000000000').substring(0, 32);
                    }
                };
            } catch (e) {
                console.error('MD5计算失败:', e);
                return {
                    toString: function() {
                        return 'fallback_md5_hash_' + Date.now().toString(16);
                    }
                };
            }
        },

        SHA256: function(message) {
            try {
                // 简化的SHA256实现（仅用于演示）
                const hash = btoa(message + 'sha256_salt').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
                return {
                    toString: function() {
                        return (hash + '0000000000000000000000000000000000000000000000000000000000000000').substring(0, 64);
                    }
                };
            } catch (e) {
                console.error('SHA256计算失败:', e);
                return {
                    toString: function() {
                        return 'fallback_sha256_hash_' + Date.now().toString(16) + generateRandomString(32);
                    }
                };
            }
        }
    };

    // PBKDF2密钥派生
    const PBKDF2 = function(password, salt, options) {
        try {
            // 简化的PBKDF2实现（仅用于演示）
            const iterations = options.iterations || 1000;
            const keySize = options.keySize || 8;

            let combined = password + salt;
            for (let i = 0; i < Math.min(iterations, 1000); i++) {
                combined = btoa(combined).replace(/[^a-zA-Z0-9]/g, '');
            }

            return {
                toString: function() {
                    const key = (combined + generateRandomString(64)).substring(0, keySize * 8);
                    return key;
                }
            };
        } catch (e) {
            console.error('PBKDF2计算失败:', e);
            return {
                toString: function() {
                    return 'fallback_key_' + generateRandomString(32);
                }
            };
        }
    };

    // WordArray实现
    const WordArray = {
        random: function(bytes) {
            try {
                return {
                    toString: function() {
                        return generateRandomString(bytes * 2);
                    }
                };
            } catch (e) {
                console.error('生成随机WordArray失败:', e);
                return {
                    toString: function() {
                        return 'fallback_random_' + Date.now().toString(16);
                    }
                };
            }
        },

        create: function(words, sigBytes) {
            return {
                words: words || [],
                sigBytes: sigBytes || 0,
                toString: function() {
                    return Array.isArray(this.words) ? this.words.join('') : String(this.words);
                }
            };
        }
    };

    // 模式和填充
    const mode = {
        CBC: 'CBC'
    };

    const pad = {
        Pkcs7: 'Pkcs7'
    };

    const enc = {
        Base64: {
            stringify: function(wordArray) {
                try {
                    return Base64.encode(wordArray.toString());
                } catch (e) {
                    console.error('Base64 stringify失败:', e);
                    return null;
                }
            },
            parse: function(base64Str) {
                return {
                    toString: function() {
                        return Base64.decode(base64Str);
                    }
                };
            }
        },
        Utf8: {
            parse: function(str) {
                return {
                    toString: function() {
                        return str;
                    }
                };
            }
        }
    };

    // 主要的CryptoJS对象
    const CryptoJS = {
        AES: AES,
        MD5: Hash.MD5,
        SHA256: Hash.SHA256,
        PBKDF2: PBKDF2,
        lib: {
            WordArray: WordArray,
            CipherParams: {
                create: function(params) {
                    return params || {};
                }
            }
        },
        mode: mode,
        pad: pad,
        enc: enc
    };

    // 将CryptoJS暴露到全局作用域
    if (typeof window !== 'undefined') {
        window.CryptoJS = CryptoJS;
    }
    if (typeof global !== 'undefined') {
        global.CryptoJS = CryptoJS;
    }

    console.log('📦 简化版CryptoJS已加载（仅用于演示）');
    console.warn('⚠️ 这是简化版本，实际项目请使用完整的CryptoJS库');

    // 测试加密功能
    try {
        const testResult = CryptoJS.AES.encrypt('test', 'key', {});
        if (testResult && testResult.ciphertext) {
            console.log('✅ CryptoJS加密功能测试通过');
        } else {
            console.error('❌ CryptoJS加密功能测试失败');
        }
    } catch (e) {
        console.error('❌ CryptoJS测试异常:', e);
    }

})();
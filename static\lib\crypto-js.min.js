// 简化的CryptoJS实现 - 仅用于演示
// 在实际项目中应使用完整的CryptoJS库

(function() {
    'use strict';

    // 简单的Base64编码/解码
    const Base64 = {
        encode: function(str) {
            return btoa(unescape(encodeURIComponent(str)));
        },
        decode: function(str) {
            return decodeURIComponent(escape(atob(str)));
        }
    };

    // 简单的AES加密实现（仅用于演示）
    const AES = {
        encrypt: function(message, key, options) {
            // 这是一个简化的实现，仅用于演示
            // 实际应用中应使用真正的AES加密
            const iv = options.iv || this.generateIV();
            const encrypted = this.simpleEncrypt(message, key.toString(), iv.toString());
            return {
                ciphertext: {
                    toString: function() {
                        return Base64.encode(encrypted);
                    }
                }
            };
        },

        decrypt: function(cipherParams, key, options) {
            const encrypted = Base64.decode(cipherParams.ciphertext.toString());
            const decrypted = this.simpleDecrypt(encrypted, key.toString(), options.iv.toString());
            return {
                toString: function() {
                    return decrypted;
                }
            };
        },

        simpleEncrypt: function(text, key, iv) {
            // 简单的XOR加密（仅用于演示）
            let result = '';
            const keyStr = key + iv;
            for (let i = 0; i < text.length; i++) {
                const keyChar = keyStr.charCodeAt(i % keyStr.length);
                const textChar = text.charCodeAt(i);
                result += String.fromCharCode(textChar ^ keyChar);
            }
            return result;
        },

        simpleDecrypt: function(encrypted, key, iv) {
            // 简单的XOR解密（仅用于演示）
            return this.simpleEncrypt(encrypted, key, iv);
        },

        generateIV: function() {
            const array = new Uint8Array(16);
            crypto.getRandomValues(array);
            return {
                toString: function() {
                    return Array.from(array).map(b => b.toString(16).padStart(2, '0')).join('');
                }
            };
        }
    };

    // 哈希函数
    const Hash = {
        MD5: function(message) {
            // 简化的MD5实现（仅用于演示）
            return {
                toString: function() {
                    return btoa(message).substring(0, 32);
                }
            };
        },

        SHA256: function(message) {
            // 简化的SHA256实现（仅用于演示）
            return {
                toString: function() {
                    return btoa(message + 'sha256').substring(0, 64);
                }
            };
        }
    };

    // PBKDF2密钥派生
    const PBKDF2 = function(password, salt, options) {
        // 简化的PBKDF2实现（仅用于演示）
        const combined = password + salt + options.iterations;
        return {
            toString: function() {
                return btoa(combined).substring(0, options.keySize * 4);
            }
        };
    };

    // WordArray实现
    const WordArray = {
        random: function(bytes) {
            const array = new Uint8Array(bytes);
            crypto.getRandomValues(array);
            return {
                toString: function() {
                    return Array.from(array).map(b => b.toString(16).padStart(2, '0')).join('');
                }
            };
        },

        create: function(words, sigBytes) {
            return {
                words: words || [],
                sigBytes: sigBytes || 0,
                toString: function() {
                    return this.words.join('');
                }
            };
        }
    };

    // 模式和填充
    const mode = {
        CBC: 'CBC'
    };

    const pad = {
        Pkcs7: 'Pkcs7'
    };

    const enc = {
        Base64: {
            stringify: function(wordArray) {
                return Base64.encode(wordArray.toString());
            },
            parse: function(base64Str) {
                return {
                    toString: function() {
                        return Base64.decode(base64Str);
                    }
                };
            }
        },
        Utf8: {
            parse: function(str) {
                return {
                    toString: function() {
                        return str;
                    }
                };
            }
        }
    };

    // 主要的CryptoJS对象
    window.CryptoJS = {
        AES: AES,
        MD5: Hash.MD5,
        SHA256: Hash.SHA256,
        PBKDF2: PBKDF2,
        lib: {
            WordArray: WordArray,
            CipherParams: {
                create: function(params) {
                    return params;
                }
            }
        },
        mode: mode,
        pad: pad,
        enc: enc
    };

    console.log('📦 简化版CryptoJS已加载（仅用于演示）');
    console.warn('⚠️ 这是简化版本，实际项目请使用完整的CryptoJS库');

})();
{% extends 'base.html' %}

{% block title %}用户登录 - JS逆向登录系统{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <h2 class="auth-title">用户登录</h2>
        <p class="auth-description">请输入您的登录凭据（数据将被加密传输）</p>
        
        <form id="loginForm" class="auth-form">
            {% csrf_token %}
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required 
                       placeholder="请输入用户名" autocomplete="username">
                <div class="input-hint">用户名将使用AES加密传输</div>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required 
                       placeholder="请输入密码" autocomplete="current-password">
                <div class="input-hint">密码将使用AES加密传输</div>
            </div>
            
            <div class="form-group">
                <button type="submit" id="loginBtn" class="auth-btn">
                    <span class="btn-text">登录</span>
                    <span class="btn-loading" style="display: none;">登录中...</span>
                </button>
            </div>
            
            <div class="form-links">
                <a href="/register/" class="link">还没有账户？立即注册</a>
            </div>
        </form>
        
        <!-- 调试信息面板（用于逆向分析） -->
        <div id="debugPanel" class="debug-panel" style="display: none;">
            <h3>调试信息 (Debug Info)</h3>
            <div class="debug-item">
                <label>原始用户名:</label>
                <span id="debugUsername"></span>
            </div>
            <div class="debug-item">
                <label>加密用户名:</label>
                <span id="debugEncryptedUsername"></span>
            </div>
            <div class="debug-item">
                <label>原始密码:</label>
                <span id="debugPassword"></span>
            </div>
            <div class="debug-item">
                <label>加密密码:</label>
                <span id="debugEncryptedPassword"></span>
            </div>
            <div class="debug-item">
                <label>时间戳:</label>
                <span id="debugTimestamp"></span>
            </div>
            <div class="debug-item">
                <label>加密密钥:</label>
                <span id="debugKey"></span>
            </div>
        </div>
        
        <div class="debug-controls">
            <button type="button" id="toggleDebug" class="debug-btn">显示调试信息</button>
        </div>
    </div>
</div>

<!-- 消息提示 -->
<div id="messageBox" class="message-box" style="display: none;">
    <span id="messageText"></span>
    <button id="closeMessage" class="close-btn">&times;</button>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/login.js"></script>
{% endblock %}

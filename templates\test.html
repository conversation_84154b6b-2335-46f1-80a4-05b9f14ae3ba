<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>加密功能测试页面</h1>
    
    <div class="test-section">
        <h2>1. CryptoJS库测试</h2>
        <button onclick="testCryptoJS()">测试CryptoJS</button>
        <div id="cryptojs-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 加密工具测试</h2>
        <button onclick="testEncryption()">测试加密工具</button>
        <div id="encryption-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 登录数据加密测试</h2>
        <button onclick="testLoginEncryption()">测试登录加密</button>
        <div id="login-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 注册数据加密测试</h2>
        <button onclick="testRegisterEncryption()">测试注册加密</button>
        <div id="register-result" class="result"></div>
    </div>

    <script src="/static/lib/crypto-js.min.js"></script>
    <script src="/static/js/encryption.js"></script>
    
    <script>
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const className = isError ? 'error' : 'success';
            element.innerHTML += `<div class="${className}">${message}</div>`;
        }
        
        function testCryptoJS() {
            const resultId = 'cryptojs-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '测试CryptoJS是否加载...');
                
                if (typeof CryptoJS === 'undefined') {
                    log(resultId, '❌ CryptoJS未定义', true);
                    return;
                }
                
                log(resultId, '✅ CryptoJS已加载');
                
                // 测试AES加密
                const testData = 'Hello World';
                const testKey = 'test_key';
                
                log(resultId, `测试数据: ${testData}`);
                log(resultId, `测试密钥: ${testKey}`);
                
                const encrypted = CryptoJS.AES.encrypt(testData, testKey, {});
                if (encrypted && encrypted.ciphertext) {
                    log(resultId, `✅ AES加密成功: ${encrypted.ciphertext.toString()}`);
                    
                    // 测试解密
                    const decrypted = CryptoJS.AES.decrypt(encrypted, testKey, {});
                    if (decrypted) {
                        const decryptedText = decrypted.toString();
                        log(resultId, `✅ AES解密成功: ${decryptedText}`);
                        
                        if (decryptedText === testData) {
                            log(resultId, '✅ 加密解密验证通过');
                        } else {
                            log(resultId, '❌ 加密解密验证失败', true);
                        }
                    } else {
                        log(resultId, '❌ AES解密失败', true);
                    }
                } else {
                    log(resultId, '❌ AES加密失败', true);
                }
                
            } catch (error) {
                log(resultId, `❌ 测试异常: ${error.message}`, true);
                console.error('CryptoJS测试异常:', error);
            }
        }
        
        function testEncryption() {
            const resultId = 'encryption-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '测试加密工具类...');
                
                if (typeof CryptoUtils === 'undefined') {
                    log(resultId, '❌ CryptoUtils未定义', true);
                    return;
                }
                
                log(resultId, '✅ CryptoUtils已加载');
                
                const testData = 'test_data_123';
                log(resultId, `测试数据: ${testData}`);
                
                const encrypted = CryptoUtils.encryptData(testData);
                if (encrypted) {
                    log(resultId, `✅ 数据加密成功: ${encrypted}`);
                    
                    const decrypted = CryptoUtils.decryptData(encrypted);
                    if (decrypted) {
                        log(resultId, `✅ 数据解密成功: ${decrypted}`);
                        
                        if (decrypted === testData) {
                            log(resultId, '✅ 加密解密验证通过');
                        } else {
                            log(resultId, '❌ 加密解密验证失败', true);
                        }
                    } else {
                        log(resultId, '❌ 数据解密失败', true);
                    }
                } else {
                    log(resultId, '❌ 数据加密失败', true);
                }
                
            } catch (error) {
                log(resultId, `❌ 测试异常: ${error.message}`, true);
                console.error('加密工具测试异常:', error);
            }
        }
        
        function testLoginEncryption() {
            const resultId = 'login-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '测试登录数据加密...');
                
                if (typeof LoginEncryptor === 'undefined') {
                    log(resultId, '❌ LoginEncryptor未定义', true);
                    return;
                }
                
                log(resultId, '✅ LoginEncryptor已加载');
                
                const username = 'test_user';
                const password = 'test_password';
                
                log(resultId, `用户名: ${username}`);
                log(resultId, `密码: ${password}`);
                
                const encryptedData = LoginEncryptor.encryptLoginData(username, password);
                if (encryptedData) {
                    log(resultId, '✅ 登录数据加密成功');
                    log(resultId, `加密用户名: ${encryptedData.encrypted_username ? '有值' : '空值'}`);
                    log(resultId, `加密密码: ${encryptedData.encrypted_password ? '有值' : '空值'}`);
                    log(resultId, `时间戳: ${encryptedData.timestamp}`);
                    log(resultId, `签名: ${encryptedData.signature}`);
                } else {
                    log(resultId, '❌ 登录数据加密失败', true);
                }
                
            } catch (error) {
                log(resultId, `❌ 测试异常: ${error.message}`, true);
                console.error('登录加密测试异常:', error);
            }
        }
        
        function testRegisterEncryption() {
            const resultId = 'register-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                log(resultId, '测试注册数据加密...');
                
                if (typeof LoginEncryptor === 'undefined') {
                    log(resultId, '❌ LoginEncryptor未定义', true);
                    return;
                }
                
                log(resultId, '✅ LoginEncryptor已加载');
                
                const username = 'test_user';
                const email = '<EMAIL>';
                const password = 'test_password';
                
                log(resultId, `用户名: ${username}`);
                log(resultId, `邮箱: ${email}`);
                log(resultId, `密码: ${password}`);
                
                const encryptedData = LoginEncryptor.encryptRegisterData(username, email, password);
                if (encryptedData) {
                    log(resultId, '✅ 注册数据加密成功');
                    log(resultId, `加密用户名: ${encryptedData.encrypted_username ? '有值' : '空值'}`);
                    log(resultId, `加密邮箱: ${encryptedData.encrypted_email ? '有值' : '空值'}`);
                    log(resultId, `加密密码: ${encryptedData.encrypted_password ? '有值' : '空值'}`);
                    log(resultId, `时间戳: ${encryptedData.timestamp}`);
                    log(resultId, `签名: ${encryptedData.signature}`);
                } else {
                    log(resultId, '❌ 注册数据加密失败', true);
                }
                
            } catch (error) {
                log(resultId, `❌ 测试异常: ${error.message}`, true);
                console.error('注册加密测试异常:', error);
            }
        }
        
        // 页面加载后自动运行所有测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testCryptoJS();
                setTimeout(() => testEncryption(), 1000);
                setTimeout(() => testLoginEncryption(), 2000);
                setTimeout(() => testRegisterEncryption(), 3000);
            }, 500);
        });
    </script>
</body>
</html>

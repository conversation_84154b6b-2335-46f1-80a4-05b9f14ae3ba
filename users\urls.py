from django.urls import path
from . import views

urlpatterns = [
    # 页面路由
    path('login/', views.LoginPageView.as_view(), name='login_page'),
    path('register/', views.RegisterPageView.as_view(), name='register_page'),
    path('dashboard/', views.DashboardView.as_view(), name='dashboard'),
    path('test/', views.TestView.as_view(), name='test_page'),

    # API路由
    path('api/register/', views.register_api, name='register_api'),
    path('api/login/', views.login_api, name='login_api'),
    path('api/logout/', views.logout_api, name='logout_api'),
    path('api/user-info/', views.user_info_api, name='user_info_api'),
]

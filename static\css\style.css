/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 认证容器样式 */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 450px;
    position: relative;
}

.auth-title {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 10px;
    color: #333;
}

.auth-description {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
    font-size: 0.95rem;
}

/* 表单样式 */
.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-hint {
    font-size: 0.85rem;
    color: #666;
    margin-top: 5px;
}

.auth-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: none;
}

.form-links {
    text-align: center;
    margin-top: 20px;
}

.link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* 调试面板样式 */
.debug-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    font-family: 'Courier New', monospace;
}

.debug-panel h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.debug-item {
    display: flex;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.debug-item label {
    font-weight: bold;
    min-width: 120px;
    color: #495057;
}

.debug-item span {
    color: #007bff;
    word-break: break-all;
    flex: 1;
}

.debug-controls {
    text-align: center;
    margin-top: 20px;
}

.debug-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.debug-btn:hover {
    background: #5a6268;
}

/* 消息提示样式 */
.message-box {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 10px;
    max-width: 400px;
}

.message-box.success {
    border-left: 4px solid #28a745;
}

.message-box.error {
    border-left: 4px solid #dc3545;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 仪表板样式 */
.dashboard-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.dashboard-header h2 {
    color: #333;
    font-size: 1.8rem;
}

.logout-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #c82333;
}

.dashboard-content {
    display: grid;
    gap: 30px;
}

.user-info-card, .login-logs-card, .security-info-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.user-info-card h3, .login-logs-card h3, .security-info-card h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.info-item span {
    color: #333;
    font-size: 1rem;
}

/* 表格样式 */
.logs-table {
    overflow-x: auto;
}

.logs-table table {
    width: 100%;
    border-collapse: collapse;
}

.logs-table th, .logs-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.logs-table th {
    background: #e9ecef;
    font-weight: 600;
    color: #495057;
}

.status.success {
    color: #28a745;
    font-weight: 600;
}

.status.failed {
    color: #dc3545;
    font-weight: 600;
}

.user-agent {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: #666;
}

.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
}

.security-tips {
    line-height: 1.6;
}

.security-tips p {
    margin-bottom: 10px;
    color: #495057;
}

/* 底部样式 */
.footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .auth-card {
        padding: 30px 20px;
        margin: 0 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}

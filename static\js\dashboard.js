/**
 * 仪表板页面JavaScript
 * 处理用户仪表板相关功能
 */

class DashboardManager {
    constructor() {
        this.logoutBtn = document.getElementById('logoutBtn');
        this.init();
    }
    
    /**
     * 初始化仪表板管理器
     */
    init() {
        // 绑定登出按钮事件
        if (this.logoutBtn) {
            this.logoutBtn.addEventListener('click', this.handleLogout.bind(this));
        }
        
        // 加载用户信息
        this.loadUserInfo();
        
        console.log('📊 仪表板管理器已初始化');
    }
    
    /**
     * 处理用户登出
     */
    async handleLogout() {
        if (!confirm('确定要退出登录吗？')) {
            return;
        }
        
        try {
            const response = await CommonUtils.request('/api/logout/', {
                method: 'POST'
            });
            
            if (response.success) {
                CommonUtils.showSuccess('已成功退出登录');
                
                // 延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = '/login/';
                }, 1000);
            } else {
                throw new Error(response.message || '退出登录失败');
            }
            
        } catch (error) {
            console.error('登出错误:', error);
            CommonUtils.showError(error.message || '退出登录失败');
        }
    }
    
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            const response = await CommonUtils.request('/api/user-info/');
            
            if (response.success) {
                console.log('用户信息:', response.user);
                // 这里可以更新页面上的用户信息显示
            }
            
        } catch (error) {
            console.error('加载用户信息失败:', error);
        }
    }
    
    /**
     * 刷新登录日志
     */
    async refreshLoginLogs() {
        try {
            // 这里可以添加刷新登录日志的逻辑
            console.log('刷新登录日志...');
            
        } catch (error) {
            console.error('刷新登录日志失败:', error);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化仪表板管理器
    const dashboardManager = new DashboardManager();
    
    // 将管理器暴露到全局作用域，便于调试
    window.dashboardManager = dashboardManager;
    
    console.log('✅ 仪表板页面已加载');
});

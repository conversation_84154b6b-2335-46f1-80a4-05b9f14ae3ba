{% extends 'base.html' %}

{% block title %}用户仪表板 - JS逆向登录系统{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h2>欢迎，{{ user.username }}！</h2>
        <div class="user-actions">
            <button id="logoutBtn" class="logout-btn">退出登录</button>
        </div>
    </div>
    
    <div class="dashboard-content">
        <div class="user-info-card">
            <h3>用户信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <label>用户名:</label>
                    <span>{{ user.username }}</span>
                </div>
                <div class="info-item">
                    <label>邮箱:</label>
                    <span>{{ user.email }}</span>
                </div>
                <div class="info-item">
                    <label>注册时间:</label>
                    <span>{{ user.created_at|date:"Y-m-d H:i:s" }}</span>
                </div>
                <div class="info-item">
                    <label>登录次数:</label>
                    <span>{{ user.login_count }}</span>
                </div>
                <div class="info-item">
                    <label>最后登录IP:</label>
                    <span>{{ user.last_login_ip|default:"未知" }}</span>
                </div>
            </div>
        </div>
        
        <div class="login-logs-card">
            <h3>最近登录记录</h3>
            <div class="logs-table">
                <table>
                    <thead>
                        <tr>
                            <th>登录时间</th>
                            <th>IP地址</th>
                            <th>状态</th>
                            <th>用户代理</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in login_logs %}
                        <tr>
                            <td>{{ log.login_time|date:"Y-m-d H:i:s" }}</td>
                            <td>{{ log.ip_address }}</td>
                            <td>
                                <span class="status {% if log.login_success %}success{% else %}failed{% endif %}">
                                    {% if log.login_success %}成功{% else %}失败{% endif %}
                                </span>
                            </td>
                            <td class="user-agent">{{ log.user_agent|truncatechars:50 }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="no-data">暂无登录记录</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="security-info-card">
            <h3>安全信息</h3>
            <div class="security-tips">
                <p><strong>注意:</strong> 本系统使用前端JavaScript加密传输敏感数据。</p>
                <p><strong>加密算法:</strong> AES-256-CBC</p>
                <p><strong>密钥派生:</strong> PBKDF2 with SHA-256</p>
                <p><strong>用途:</strong> 仅用于逆向工程学习和测试</p>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示 -->
<div id="messageBox" class="message-box" style="display: none;">
    <span id="messageText"></span>
    <button id="closeMessage" class="close-btn">&times;</button>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/dashboard.js"></script>
{% endblock %}

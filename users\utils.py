"""
加密解密工具函数
用于处理前端JavaScript加密的数据
"""
import base64
import json
from cryptography.fernet import Fe<PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings


def get_encryption_key():
    """
    生成加密密钥
    基于settings中的密钥生成Fernet密钥
    """
    password = settings.ENCRYPTION_KEY.encode()
    salt = b'reverse_engineering_salt_2024'  # 固定盐值，便于逆向分析
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(password))
    return Fernet(key)


def decrypt_data(encrypted_data):
    """
    解密前端传输的数据
    
    Args:
        encrypted_data (str): Base64编码的加密数据
    
    Returns:
        str: 解密后的原始数据
    """
    try:
        # 获取加密器
        cipher = get_encryption_key()
        
        # Base64解码
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        
        # 解密数据
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        
        # 转换为字符串
        decrypted_data = decrypted_bytes.decode('utf-8')
        
        return decrypted_data
        
    except Exception as e:
        print(f"解密失败: {str(e)}")
        return None


def encrypt_data(plain_data):
    """
    加密数据（用于测试）
    
    Args:
        plain_data (str): 原始数据
    
    Returns:
        str: Base64编码的加密数据
    """
    try:
        # 获取加密器
        cipher = get_encryption_key()
        
        # 加密数据
        encrypted_bytes = cipher.encrypt(plain_data.encode('utf-8'))
        
        # Base64编码
        encrypted_data = base64.b64encode(encrypted_bytes).decode()
        
        return encrypted_data
        
    except Exception as e:
        print(f"加密失败: {str(e)}")
        return None


def get_client_ip(request):
    """
    获取客户端真实IP地址
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def log_login_attempt(user, request, encrypted_data, success=True):
    """
    记录登录尝试
    """
    from .models import LoginLog
    
    LoginLog.objects.create(
        user=user,
        ip_address=get_client_ip(request),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        encrypted_data=encrypted_data,
        login_success=success
    )

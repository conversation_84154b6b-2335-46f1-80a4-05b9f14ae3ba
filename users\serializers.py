from rest_framework import serializers
from django.contrib.auth import authenticate
from .models import CustomUser, LoginLog
from .utils import decrypt_data
import json


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    用户注册序列化器
    处理前端加密数据的注册请求
    """
    encrypted_username = serializers.CharField(write_only=True, help_text='加密的用户名')
    encrypted_password = serializers.CharField(write_only=True, help_text='加密的密码')
    encrypted_email = serializers.Char<PERSON>ield(write_only=True, help_text='加密的邮箱')
    
    class Meta:
        model = CustomUser
        fields = ['encrypted_username', 'encrypted_password', 'encrypted_email']
    
    def validate(self, attrs):
        """
        验证并解密前端传输的数据
        """
        try:
            # 解密用户数据
            username = decrypt_data(attrs['encrypted_username'])
            password = decrypt_data(attrs['encrypted_password'])
            email = decrypt_data(attrs['encrypted_email'])
            
            # 验证解密后的数据
            if not username or not password or not email:
                raise serializers.ValidationError('数据解密失败')
            
            # 检查用户名是否已存在
            if CustomUser.objects.filter(username=username).exists():
                raise serializers.ValidationError('用户名已存在')
            
            # 检查邮箱是否已存在
            if CustomUser.objects.filter(email=email).exists():
                raise serializers.ValidationError('邮箱已被注册')
            
            attrs['username'] = username
            attrs['password'] = password
            attrs['email'] = email
            
        except Exception as e:
            raise serializers.ValidationError(f'数据处理错误: {str(e)}')
        
        return attrs
    
    def create(self, validated_data):
        """
        创建新用户
        """
        # 移除加密字段
        validated_data.pop('encrypted_username', None)
        validated_data.pop('encrypted_password', None)
        validated_data.pop('encrypted_email', None)
        
        # 创建用户
        user = CustomUser.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=validated_data['password']
        )
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    用户登录序列化器
    处理前端加密数据的登录请求
    """
    encrypted_username = serializers.CharField(help_text='加密的用户名')
    encrypted_password = serializers.CharField(help_text='加密的密码')
    timestamp = serializers.CharField(help_text='时间戳')
    
    def validate(self, attrs):
        """
        验证登录数据并进行用户认证
        """
        try:
            # 解密登录数据
            username = decrypt_data(attrs['encrypted_username'])
            password = decrypt_data(attrs['encrypted_password'])
            
            if not username or not password:
                raise serializers.ValidationError('登录数据解密失败')
            
            # 验证用户凭据
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('用户名或密码错误')
            
            if not user.is_active:
                raise serializers.ValidationError('用户账户已被禁用')
            
            attrs['user'] = user
            attrs['decrypted_username'] = username
            
        except Exception as e:
            raise serializers.ValidationError(f'登录验证失败: {str(e)}')
        
        return attrs


class LoginLogSerializer(serializers.ModelSerializer):
    """
    登录日志序列化器
    """
    username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = LoginLog
        fields = ['id', 'username', 'login_time', 'ip_address', 'user_agent', 'login_success']

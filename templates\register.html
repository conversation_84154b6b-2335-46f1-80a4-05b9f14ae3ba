{% extends 'base.html' %}

{% block title %}用户注册 - JS逆向登录系统{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <h2 class="auth-title">用户注册</h2>
        <p class="auth-description">创建新账户（所有数据将被加密传输）</p>
        
        <form id="registerForm" class="auth-form">
            {% csrf_token %}
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required 
                       placeholder="请输入用户名" autocomplete="username">
                <div class="input-hint">用户名长度3-20个字符</div>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱地址:</label>
                <input type="email" id="email" name="email" required 
                       placeholder="请输入邮箱地址" autocomplete="email">
                <div class="input-hint">请输入有效的邮箱地址</div>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required 
                       placeholder="请输入密码" autocomplete="new-password">
                <div class="input-hint">密码长度至少6个字符</div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">确认密码:</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required 
                       placeholder="请再次输入密码" autocomplete="new-password">
                <div class="input-hint">请再次输入相同的密码</div>
            </div>
            
            <div class="form-group">
                <button type="submit" id="registerBtn" class="auth-btn">
                    <span class="btn-text">注册</span>
                    <span class="btn-loading" style="display: none;">注册中...</span>
                </button>
            </div>
            
            <div class="form-links">
                <a href="/login/" class="link">已有账户？立即登录</a>
            </div>
        </form>
        
        <!-- 调试信息面板（用于逆向分析） -->
        <div id="debugPanel" class="debug-panel" style="display: none;">
            <h3>调试信息 (Debug Info)</h3>
            <div class="debug-item">
                <label>原始用户名:</label>
                <span id="debugUsername"></span>
            </div>
            <div class="debug-item">
                <label>加密用户名:</label>
                <span id="debugEncryptedUsername"></span>
            </div>
            <div class="debug-item">
                <label>原始邮箱:</label>
                <span id="debugEmail"></span>
            </div>
            <div class="debug-item">
                <label>加密邮箱:</label>
                <span id="debugEncryptedEmail"></span>
            </div>
            <div class="debug-item">
                <label>原始密码:</label>
                <span id="debugPassword"></span>
            </div>
            <div class="debug-item">
                <label>加密密码:</label>
                <span id="debugEncryptedPassword"></span>
            </div>
            <div class="debug-item">
                <label>时间戳:</label>
                <span id="debugTimestamp"></span>
            </div>
        </div>
        
        <div class="debug-controls">
            <button type="button" id="toggleDebug" class="debug-btn">显示调试信息</button>
        </div>
    </div>
</div>

<!-- 消息提示 -->
<div id="messageBox" class="message-box" style="display: none;">
    <span id="messageText"></span>
    <button id="closeMessage" class="close-btn">&times;</button>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/register.js"></script>
{% endblock %}

from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone


class CustomUser(AbstractUser):
    """
    自定义用户模型，扩展Django默认用户模型
    用于逆向工程测试的用户认证系统
    """
    email = models.EmailField('邮箱地址', unique=True)
    phone = models.CharField('手机号码', max_length=11, blank=True, null=True)
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    last_login_ip = models.GenericIPAddressField('最后登录IP', blank=True, null=True)
    login_count = models.IntegerField('登录次数', default=0)
    
    class Meta:
        db_table = 'auth_user_custom'
        verbose_name = '用户'
        verbose_name_plural = '用户管理'
    
    def __str__(self):
        return self.username


class LoginLog(models.Model):
    """
    登录日志模型，记录用户登录行为
    用于逆向分析和安全审计
    """
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, verbose_name='用户')
    login_time = models.DateTimeField('登录时间', default=timezone.now)
    ip_address = models.GenericIPAddressField('登录IP')
    user_agent = models.TextField('用户代理', blank=True)
    encrypted_data = models.TextField('加密数据', blank=True, help_text='前端传输的加密数据')
    login_success = models.BooleanField('登录成功', default=True)
    
    class Meta:
        db_table = 'login_log'
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        ordering = ['-login_time']
    
    def __str__(self):
        return f'{self.user.username} - {self.login_time}'

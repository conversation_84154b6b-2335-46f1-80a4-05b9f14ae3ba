/**
 * 前端加密工具类
 * 用于逆向工程分析的JavaScript加密实现
 * 
 * 注意：此代码仅用于教育和测试目的
 * 实际生产环境中不应将加密逻辑完全暴露在前端
 */

// 加密配置常量 - 便于逆向分析时定位
const ENCRYPTION_CONFIG = {
    // 基础密钥 - 在实际应用中应该动态生成
    SECRET_KEY: 'reverse-engineering-demo-key-2024',
    
    // 盐值 - 用于增强安全性
    SALT: 'reverse_engineering_salt_2024',
    
    // 迭代次数 - PBKDF2密钥派生
    ITERATIONS: 100000,
    
    // 密钥长度
    KEY_LENGTH: 256,
    
    // IV长度
    IV_LENGTH: 128
};

/**
 * 加密工具类
 * 包含所有加密相关的方法
 */
class CryptoUtils {
    
    /**
     * 生成密钥
     * 使用PBKDF2算法从基础密钥派生加密密钥
     */
    static generateKey() {
        // 将配置转换为CryptoJS格式
        const key = CryptoJS.PBKDF2(
            ENCRYPTION_CONFIG.SECRET_KEY, 
            ENCRYPTION_CONFIG.SALT, {
                keySize: ENCRYPTION_CONFIG.KEY_LENGTH / 32,
                iterations: ENCRYPTION_CONFIG.ITERATIONS
            }
        );
        return key;
    }
    
    /**
     * 生成随机IV
     */
    static generateIV() {
        return CryptoJS.lib.WordArray.random(ENCRYPTION_CONFIG.IV_LENGTH / 8);
    }
    
    /**
     * AES加密函数
     * @param {string} plaintext - 要加密的明文
     * @returns {string} Base64编码的加密数据
     */
    static encryptData(plaintext) {
        try {
            // 生成密钥和IV
            const key = this.generateKey();
            const iv = this.generateIV();
            
            // 执行AES-CBC加密
            const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });
            
            // 将IV和加密数据组合
            const combined = iv.concat(encrypted.ciphertext);
            
            // 返回Base64编码的结果
            return CryptoJS.enc.Base64.stringify(combined);
            
        } catch (error) {
            console.error('加密失败:', error);
            return null;
        }
    }
    
    /**
     * AES解密函数（用于前端验证，实际解密在后端进行）
     * @param {string} encryptedData - Base64编码的加密数据
     * @returns {string} 解密后的明文
     */
    static decryptData(encryptedData) {
        try {
            // Base64解码
            const combined = CryptoJS.enc.Base64.parse(encryptedData);
            
            // 提取IV和密文
            const iv = CryptoJS.lib.WordArray.create(
                combined.words.slice(0, ENCRYPTION_CONFIG.IV_LENGTH / 32)
            );
            const ciphertext = CryptoJS.lib.WordArray.create(
                combined.words.slice(ENCRYPTION_CONFIG.IV_LENGTH / 32)
            );
            
            // 生成密钥
            const key = this.generateKey();
            
            // 执行解密
            const decrypted = CryptoJS.AES.decrypt(
                CryptoJS.lib.CipherParams.create({
                    ciphertext: ciphertext
                }), 
                key, {
                    iv: iv,
                    mode: CryptoJS.mode.CBC,
                    padding: CryptoJS.pad.Pkcs7
                }
            );
            
            return decrypted.toString(CryptoJS.enc.Utf8);
            
        } catch (error) {
            console.error('解密失败:', error);
            return null;
        }
    }
    
    /**
     * 生成时间戳
     * 用于防重放攻击
     */
    static generateTimestamp() {
        return Date.now().toString();
    }
    
    /**
     * 生成随机字符串
     * 用于增加加密的随机性
     */
    static generateRandomString(length = 16) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    /**
     * 计算数据的MD5哈希值
     * 用于数据完整性验证
     */
    static calculateMD5(data) {
        return CryptoJS.MD5(data).toString();
    }
    
    /**
     * 计算数据的SHA256哈希值
     */
    static calculateSHA256(data) {
        return CryptoJS.SHA256(data).toString();
    }
}

/**
 * 登录数据加密器
 * 专门用于处理登录相关的数据加密
 */
class LoginEncryptor {
    
    /**
     * 加密登录数据
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Object} 包含加密数据的对象
     */
    static encryptLoginData(username, password) {
        try {
            // 生成时间戳
            const timestamp = CryptoUtils.generateTimestamp();
            
            // 加密用户名和密码
            const encryptedUsername = CryptoUtils.encryptData(username);
            const encryptedPassword = CryptoUtils.encryptData(password);
            
            // 生成数据签名（用于完整性验证）
            const signature = CryptoUtils.calculateSHA256(
                username + password + timestamp + ENCRYPTION_CONFIG.SECRET_KEY
            );
            
            return {
                encrypted_username: encryptedUsername,
                encrypted_password: encryptedPassword,
                timestamp: timestamp,
                signature: signature
            };
            
        } catch (error) {
            console.error('登录数据加密失败:', error);
            return null;
        }
    }
    
    /**
     * 加密注册数据
     * @param {string} username - 用户名
     * @param {string} email - 邮箱
     * @param {string} password - 密码
     * @returns {Object} 包含加密数据的对象
     */
    static encryptRegisterData(username, email, password) {
        try {
            // 生成时间戳
            const timestamp = CryptoUtils.generateTimestamp();
            
            // 加密所有数据
            const encryptedUsername = CryptoUtils.encryptData(username);
            const encryptedEmail = CryptoUtils.encryptData(email);
            const encryptedPassword = CryptoUtils.encryptData(password);
            
            // 生成数据签名
            const signature = CryptoUtils.calculateSHA256(
                username + email + password + timestamp + ENCRYPTION_CONFIG.SECRET_KEY
            );
            
            return {
                encrypted_username: encryptedUsername,
                encrypted_email: encryptedEmail,
                encrypted_password: encryptedPassword,
                timestamp: timestamp,
                signature: signature
            };
            
        } catch (error) {
            console.error('注册数据加密失败:', error);
            return null;
        }
    }
}

// 调试工具 - 用于逆向分析
const DebugUtils = {
    
    /**
     * 显示加密过程的详细信息
     */
    showEncryptionDetails: function(plaintext, encrypted) {
        console.group('🔐 加密详情');
        console.log('原始数据:', plaintext);
        console.log('加密后数据:', encrypted);
        console.log('密钥配置:', ENCRYPTION_CONFIG);
        console.log('加密算法: AES-256-CBC');
        console.log('密钥派生: PBKDF2-SHA256');
        console.groupEnd();
    },
    
    /**
     * 显示当前加密配置
     */
    showConfig: function() {
        console.table(ENCRYPTION_CONFIG);
    },
    
    /**
     * 测试加密解密功能
     */
    testEncryption: function(testData = 'Hello World') {
        console.group('🧪 加密测试');
        const encrypted = CryptoUtils.encryptData(testData);
        const decrypted = CryptoUtils.decryptData(encrypted);
        
        console.log('测试数据:', testData);
        console.log('加密结果:', encrypted);
        console.log('解密结果:', decrypted);
        console.log('测试结果:', testData === decrypted ? '✅ 通过' : '❌ 失败');
        console.groupEnd();
    }
};

// 将工具类暴露到全局作用域，便于调试和逆向分析
window.CryptoUtils = CryptoUtils;
window.LoginEncryptor = LoginEncryptor;
window.DebugUtils = DebugUtils;
window.ENCRYPTION_CONFIG = ENCRYPTION_CONFIG;

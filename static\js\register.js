/**
 * 注册页面JavaScript
 * 处理用户注册逻辑和前端加密
 */

class RegisterManager {
    constructor() {
        this.form = document.getElementById('registerForm');
        this.usernameInput = document.getElementById('username');
        this.emailInput = document.getElementById('email');
        this.passwordInput = document.getElementById('password');
        this.confirmPasswordInput = document.getElementById('confirmPassword');
        this.registerBtn = document.getElementById('registerBtn');
        this.debugPanel = document.getElementById('debugPanel');
        
        this.init();
    }
    
    /**
     * 初始化注册管理器
     */
    init() {
        if (!this.form) return;
        
        // 绑定表单提交事件
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
        
        // 绑定输入框事件
        this.usernameInput?.addEventListener('input', this.clearErrors.bind(this));
        this.emailInput?.addEventListener('input', this.clearErrors.bind(this));
        this.passwordInput?.addEventListener('input', this.clearErrors.bind(this));
        this.confirmPasswordInput?.addEventListener('input', this.clearErrors.bind(this));
        
        // 实时密码强度检查
        this.passwordInput?.addEventListener('input', this.checkPasswordStrength.bind(this));
        
        console.log('📝 注册管理器已初始化');
        console.log('💡 提示：打开浏览器开发者工具查看加密过程');
    }
    
    /**
     * 处理表单提交
     */
    async handleSubmit(event) {
        event.preventDefault();
        
        // 获取表单数据
        const username = this.usernameInput.value.trim();
        const email = this.emailInput.value.trim();
        const password = this.passwordInput.value;
        const confirmPassword = this.confirmPasswordInput.value;
        
        // 验证输入数据
        if (!this.validateInput(username, email, password, confirmPassword)) {
            return;
        }
        
        // 设置按钮加载状态
        CommonUtils.setButtonLoading(this.registerBtn, true);
        
        try {
            // 加密注册数据
            const encryptedData = LoginEncryptor.encryptRegisterData(username, email, password);
            
            if (!encryptedData) {
                throw new Error('数据加密失败');
            }
            
            // 更新调试面板
            this.updateDebugPanel(username, email, password, encryptedData);
            
            // 发送注册请求
            const response = await CommonUtils.request('/api/register/', {
                method: 'POST',
                data: encryptedData
            });
            
            if (response.success) {
                CommonUtils.showSuccess('注册成功！正在跳转到登录页面...');
                
                // 延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = '/login/';
                }, 2000);
            } else {
                throw new Error(response.message || '注册失败');
            }
            
        } catch (error) {
            console.error('注册错误:', error);
            CommonUtils.showError(error.message || '注册失败，请重试');
        } finally {
            // 恢复按钮状态
            CommonUtils.setButtonLoading(this.registerBtn, false);
        }
    }
    
    /**
     * 验证输入数据
     */
    validateInput(username, email, password, confirmPassword) {
        // 验证用户名
        if (!username) {
            CommonUtils.showError('请输入用户名');
            this.usernameInput.focus();
            return false;
        }
        
        if (username.length < 3 || username.length > 20) {
            CommonUtils.showError('用户名长度应在3-20个字符之间');
            this.usernameInput.focus();
            return false;
        }
        
        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            CommonUtils.showError('用户名只能包含字母、数字和下划线');
            this.usernameInput.focus();
            return false;
        }
        
        // 验证邮箱
        if (!email) {
            CommonUtils.showError('请输入邮箱地址');
            this.emailInput.focus();
            return false;
        }
        
        if (!CommonUtils.validateEmail(email)) {
            CommonUtils.showError('请输入有效的邮箱地址');
            this.emailInput.focus();
            return false;
        }
        
        // 验证密码
        if (!password) {
            CommonUtils.showError('请输入密码');
            this.passwordInput.focus();
            return false;
        }
        
        const passwordValidation = CommonUtils.validatePassword(password);
        if (!passwordValidation.isValid) {
            CommonUtils.showError(passwordValidation.errors.join('，'));
            this.passwordInput.focus();
            return false;
        }
        
        // 验证确认密码
        if (!confirmPassword) {
            CommonUtils.showError('请确认密码');
            this.confirmPasswordInput.focus();
            return false;
        }
        
        if (password !== confirmPassword) {
            CommonUtils.showError('两次输入的密码不一致');
            this.confirmPasswordInput.focus();
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查密码强度
     */
    checkPasswordStrength() {
        const password = this.passwordInput.value;
        const validation = CommonUtils.validatePassword(password);
        
        // 这里可以添加密码强度指示器的逻辑
        // 例如显示密码强度条或提示信息
        
        if (password.length > 0 && !validation.isValid) {
            console.log('密码强度检查:', validation.errors);
        }
    }
    
    /**
     * 清除错误状态
     */
    clearErrors() {
        // 移除输入框的错误样式
        this.usernameInput?.classList.remove('error');
        this.emailInput?.classList.remove('error');
        this.passwordInput?.classList.remove('error');
        this.confirmPasswordInput?.classList.remove('error');
    }
    
    /**
     * 更新调试面板信息
     */
    updateDebugPanel(username, email, password, encryptedData) {
        if (!this.debugPanel) return;
        
        // 更新调试信息
        const debugElements = {
            debugUsername: username,
            debugEmail: email,
            debugPassword: password,
            debugEncryptedUsername: encryptedData.encrypted_username,
            debugEncryptedEmail: encryptedData.encrypted_email,
            debugEncryptedPassword: encryptedData.encrypted_password,
            debugTimestamp: encryptedData.timestamp
        };
        
        Object.entries(debugElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
        
        // 在控制台输出详细信息
        console.group('🔐 注册数据加密详情');
        console.log('原始用户名:', username);
        console.log('原始邮箱:', email);
        console.log('原始密码:', password);
        console.log('加密用户名:', encryptedData.encrypted_username);
        console.log('加密邮箱:', encryptedData.encrypted_email);
        console.log('加密密码:', encryptedData.encrypted_password);
        console.log('时间戳:', encryptedData.timestamp);
        console.log('数据签名:', encryptedData.signature);
        console.log('加密配置:', ENCRYPTION_CONFIG);
        console.groupEnd();
        
        // 显示数据完整性验证信息
        console.group('🔒 数据完整性验证');
        console.log('签名算法: SHA-256');
        console.log('签名数据:', username + email + password + encryptedData.timestamp + ENCRYPTION_CONFIG.SECRET_KEY);
        console.log('生成的签名:', encryptedData.signature);
        console.groupEnd();
    }
    
    /**
     * 演示注册数据加密过程
     */
    demonstrateRegisterEncryption() {
        console.group('🎓 注册加密过程演示');
        
        const testData = {
            username: 'demo_user',
            email: '<EMAIL>',
            password: 'demo123456'
        };
        
        console.log('1. 原始注册数据:', testData);
        
        const encryptedData = LoginEncryptor.encryptRegisterData(
            testData.username, 
            testData.email, 
            testData.password
        );
        
        console.log('2. 加密后的数据:', encryptedData);
        
        // 验证解密
        const decryptedUsername = CryptoUtils.decryptData(encryptedData.encrypted_username);
        const decryptedEmail = CryptoUtils.decryptData(encryptedData.encrypted_email);
        const decryptedPassword = CryptoUtils.decryptData(encryptedData.encrypted_password);
        
        console.log('3. 解密验证:');
        console.log('   用户名:', decryptedUsername, decryptedUsername === testData.username ? '✅' : '❌');
        console.log('   邮箱:', decryptedEmail, decryptedEmail === testData.email ? '✅' : '❌');
        console.log('   密码:', decryptedPassword, decryptedPassword === testData.password ? '✅' : '❌');
        
        console.groupEnd();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化注册管理器
    const registerManager = new RegisterManager();
    
    // 将注册管理器暴露到全局作用域，便于调试
    window.registerManager = registerManager;
    
    // 添加调试命令
    window.demoRegisterEncryption = () => registerManager.demonstrateRegisterEncryption();
    
    // 在控制台显示可用的调试命令
    console.group('🛠️ 可用的调试命令');
    console.log('demoRegisterEncryption() - 演示注册加密过程');
    console.log('testCrypto() - 测试加密解密功能');
    console.log('showConfig() - 显示加密配置');
    console.log('registerManager - 访问注册管理器实例');
    console.groupEnd();
    
    // 检查是否有CryptoJS库
    if (typeof CryptoJS === 'undefined') {
        console.error('❌ CryptoJS库未加载，请检查库文件');
        CommonUtils.showError('加密库加载失败，请刷新页面重试');
    } else {
        console.log('✅ CryptoJS库已加载');
    }
});

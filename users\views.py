from django.shortcuts import render, redirect
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from .serializers import UserRegistrationSerializer, UserLoginSerializer, LoginLogSerializer
from .models import CustomUser, LoginLog
from .utils import get_client_ip, log_login_attempt
import json


class LoginPageView(View):
    """
    登录页面视图
    """
    def get(self, request):
        if request.user.is_authenticated:
            return redirect('/dashboard/')
        return render(request, 'login.html')


class RegisterPageView(View):
    """
    注册页面视图
    """
    def get(self, request):
        if request.user.is_authenticated:
            return redirect('/dashboard/')
        return render(request, 'register.html')


class DashboardView(View):
    """
    用户仪表板页面
    """
    @method_decorator(login_required)
    def get(self, request):
        return render(request, 'dashboard.html', {
            'user': request.user,
            'login_logs': LoginLog.objects.filter(user=request.user)[:10]
        })


class TestView(View):
    """
    加密功能测试页面
    """
    def get(self, request):
        return render(request, 'test.html')


@api_view(['POST'])
@permission_classes([AllowAny])
def register_api(request):
    """
    用户注册API接口
    接收前端加密数据并处理注册逻辑
    """
    try:
        print(f"注册请求数据: {request.data}")

        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            # 记录注册成功日志
            log_login_attempt(
                user=user,
                request=request,
                encrypted_data=json.dumps(request.data),
                success=True
            )

            return Response({
                'success': True,
                'message': '注册成功',
                'user_id': user.id
            }, status=status.HTTP_201_CREATED)
        else:
            print(f"注册验证失败: {serializer.errors}")
            return Response({
                'success': False,
                'message': '注册失败',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        print(f"注册异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return Response({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def login_api(request):
    """
    用户登录API接口
    接收前端加密数据并处理登录逻辑
    """
    try:
        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']

            # 执行登录
            login(request, user)

            # 更新用户登录信息
            user.login_count += 1
            user.last_login_ip = get_client_ip(request)
            user.save()

            # 记录登录成功日志
            log_login_attempt(
                user=user,
                request=request,
                encrypted_data=json.dumps(request.data),
                success=True
            )

            return Response({
                'success': True,
                'message': '登录成功',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'login_count': user.login_count
                }
            }, status=status.HTTP_200_OK)
        else:
            # 记录登录失败日志（如果能获取到用户）
            try:
                username = serializer.validated_data.get('decrypted_username')
                if username:
                    user = CustomUser.objects.filter(username=username).first()
                    if user:
                        log_login_attempt(
                            user=user,
                            request=request,
                            encrypted_data=json.dumps(request.data),
                            success=False
                        )
            except:
                pass

            return Response({
                'success': False,
                'message': '登录失败',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def logout_api(request):
    """
    用户登出API接口
    """
    try:
        logout(request)
        return Response({
            'success': True,
            'message': '登出成功'
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'登出失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def user_info_api(request):
    """
    获取当前用户信息API
    """
    if request.user.is_authenticated:
        return Response({
            'success': True,
            'user': {
                'id': request.user.id,
                'username': request.user.username,
                'email': request.user.email,
                'login_count': request.user.login_count,
                'last_login_ip': request.user.last_login_ip,
                'created_at': request.user.created_at
            }
        })
    else:
        return Response({
            'success': False,
            'message': '用户未登录'
        }, status=status.HTTP_401_UNAUTHORIZED)
